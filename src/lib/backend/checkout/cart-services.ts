import { SiteCartSummaryResponse } from '~/data/models/SiteCartSummaryResponse';

import { ApiRequest } from '../../fetch-backend/index.types';
import { fetchWithErrorHandling } from '../../fetch-backend/legacy';
import { AsyncResponse } from '../../fetch/index.types';

interface BackendCreateCartServicesProps {
  id: string;
  input: {
    installerId: number;
    services: Array<{
      imagesUrl: string;
      notes: string;
      serviceId: number;
      serviceName: string;
    }>;
    sessionId: string;
  };
  userTime?: string;
  vwo_user?: string;
}

export async function backendCreateCartServices(
  props: BackendCreateCartServicesProps,
  request?: ApiRequest,
): Promise<AsyncResponse<SiteCartSummaryResponse>> {
  const query = {
    ...(props.userTime && { userTime: props.userTime }),
    ...(props.vwo_user && { vwo_user: props.vwo_user }),
  };

  return await fetchWithErrorHandling<
    SiteCartSummaryResponse,
    typeof props.input
  >({
    endpoint: `/v2/site/cart/${props.id}/services`,
    includeAuthorization: true,
    jsonBody: props.input,
    method: 'post',
    query,
    request,
  });
}

export async function backendGetCartServices(
  props: BackendCreateCartServicesProps,
  request?: ApiRequest,
): Promise<AsyncResponse<SiteCartSummaryResponse>> {
  const query = {
    ...(props.userTime && { userTime: props.userTime }),
    ...(props.vwo_user && { vwo_user: props.vwo_user }),
  };

  return await fetchWithErrorHandling<SiteCartSummaryResponse>({
    endpoint: `/v2/site/cart/${props.id}/services`,
    includeAuthorization: true,
    method: 'get',
    query,
    request,
  });
}
